# -*- coding: utf-8 -*-
"""
组织架构应用URL配置
包含管理端和认证相关的路由
"""

from django.urls import path, include
from . import views
from . import views_permissions
from . import views_anonymous

app_name = 'organizations'

# 管理端URL配置
admin_urlpatterns = [
    # 仪表板
    path('', views.DashboardView.as_view(), name='dashboard'),
    
    # 认证相关
    path('login/', views.LoginView.as_view(), name='login'),
    path('logout/', views.LogoutView.as_view(), name='logout'),
    
    # JWT认证API
    path('api/auth/refresh/', views.TokenRefreshView.as_view(), name='token_refresh'),
    
    # 部门管理
    path('departments/', views.DepartmentListView.as_view(), name='department_list'),
    path('departments/create/', views.DepartmentCreateView.as_view(), name='department_create'),
    path('departments/<int:pk>/', views.DepartmentDetailView.as_view(), name='department_detail'),
    path('departments/<int:pk>/edit/', views.DepartmentUpdateView.as_view(), name='department_update'),
    path('departments/<int:pk>/delete/', views.DepartmentDeleteView.as_view(), name='department_delete'),
    
    # 职位管理
    path('positions/', views.PositionListView.as_view(), name='position_list'),
    path('positions/create/', views.PositionCreateView.as_view(), name='position_create'),
    path('positions/<int:pk>/', views.PositionDetailView.as_view(), name='position_detail'),
    path('positions/<int:pk>/edit/', views.PositionUpdateView.as_view(), name='position_update'),
    path('positions/<int:pk>/delete/', views.PositionDeleteView.as_view(), name='position_delete'),
    
    # 员工管理
    path('staff/', views.StaffListView.as_view(), name='staff_list'),
    path('staff/create/', views.StaffCreateView.as_view(), name='staff_create'),
    path('staff/<int:pk>/', views.StaffDetailView.as_view(), name='staff_detail'),
    path('staff/<int:pk>/edit/', views.StaffUpdateView.as_view(), name='staff_update'),
    path('staff/<int:pk>/delete/', views.StaffDeleteView.as_view(), name='staff_delete'),
    path('staff/<int:pk>/reset-password/', views.StaffResetPasswordView.as_view(), name='staff_reset_password'),
    
    # Excel导入导出
    path('import/departments/', views.DepartmentImportView.as_view(), name='department_import'),
    path('import/positions/', views.PositionImportView.as_view(), name='position_import'),
    path('import/staff/', views.StaffImportView.as_view(), name='staff_import'),
    path('export/staff/', views.StaffExportView.as_view(), name='staff_export'),
    path('export/departments/', views.DepartmentExportView.as_view(), name='department_export'),
    path('export/positions/', views.PositionExportView.as_view(), name='position_export'),
    
    # Excel模板下载
    path('excel/template/<str:template_type>/', views.ExcelTemplateDownloadView.as_view(), name='excel_template'),
    
    # 导入历史记录
    path('import/history/', views.ImportHistoryView.as_view(), name='import_history'),
    
    # 个人资料管理
    path('profile/', views.ProfileManagementView.as_view(), name='profile_management'),
    path('profile/change-password/', views.ChangePasswordView.as_view(), name='change_password'),
    
    # 权限管理
    path('permissions/', views_permissions.PermissionManagementView.as_view(), name='permissions_manage'),
    path('permissions/staff/<int:staff_id>/role/edit/', views_permissions.StaffRoleEditView.as_view(), name='staff_role_edit'),
    path('permissions/role/<str:role>/details/', views_permissions.RolePermissionsDetailView.as_view(), name='permissions_role_details'),
    path('permissions/batch/assign/', views_permissions.BatchRoleAssignView.as_view(), name='batch_role_assign'),
    path('permissions/test/', views_permissions.PermissionTestView.as_view(), name='permission_test'),
    
    # 权限管理AJAX API
    path('api/permissions/staff/role/edit/', views_permissions.edit_staff_role_ajax, name='edit_staff_role'),
    path('api/permissions/batch/assign/', views_permissions.batch_role_assignment_ajax, name='batch_role_assignment'),
    path('api/permissions/test/', views_permissions.test_permissions_system, name='test_permissions'),
    path('api/permissions/role/<str:role_code>/details/', views_permissions.role_details, name='role_details'),
    path('api/permissions/staff/<int:staff_id>/permissions/', views_permissions.staff_permissions, name='permissions_staff_permissions'),
    path('api/permissions/staff/<int:staff_id>/history/', views_permissions.staff_history, name='permissions_staff_history'),
    
    # 员工删除API
    path('api/permissions/staff/<int:staff_id>/delete/', views_permissions.StaffDeleteView.as_view(), name='staff_delete_permission'),
    path('api/permissions/staff/batch-delete/', views_permissions.StaffBatchDeleteView.as_view(), name='staff_batch_delete_permission'),
    
    # 匿名编号管理
    path('anonymous-codes/', views_anonymous.AnonymousCodesManageView.as_view(), name='anonymous_codes_manage'),
    path('anonymous-codes/migrate/batch/', views_anonymous.migrate_anonymous_codes_batch, name='migrate_anonymous_codes_batch'),
    path('anonymous-codes/generate/single/', views_anonymous.generate_single_anonymous_code, name='generate_single_anonymous_code'),
    path('anonymous-codes/staff/<int:staff_id>/history/', views_anonymous.anonymous_code_history, name='anonymous_code_history'),
    path('anonymous-codes/status/check/', views_anonymous.check_anonymous_codes_status, name='check_anonymous_codes_status'),
    path('anonymous-codes/validate/', views_anonymous.validate_anonymous_code, name='validate_anonymous_code'),
    path('anonymous-codes/export/', views_anonymous.anonymous_codes_export, name='anonymous_codes_export'),
    path('anonymous-codes/settings/', views_anonymous.anonymous_system_settings, name='anonymous_system_settings'),
    
    # 匿名编号删除API
    path('api/anonymous-codes/<int:staff_id>/delete/', views_anonymous.AnonymousCodeDeleteView.as_view(), name='anonymous_code_delete'),
    path('api/anonymous-codes/batch-delete/', views_anonymous.AnonymousCodeBatchDeleteView.as_view(), name='anonymous_code_batch_delete'),
    
    # 编号生成API
    path('api/generate-anonymous-code/', views.GenerateAnonymousCodeView.as_view(), name='generate_anonymous_code'),
    
    # 批量操作API
    path('api/staff/batch/status/', views.StaffBatchStatusView.as_view(), name='staff_batch_status'),
    path('api/staff/batch/delete/', views.StaffBatchDeleteView.as_view(), name='staff_batch_delete'),
    path('api/staff/batch/department/', views.StaffBatchDepartmentView.as_view(), name='staff_batch_department'),
    path('api/staff/batch/role/', views.StaffBatchRoleView.as_view(), name='staff_batch_role'),
    path('api/staff/batch/export/', views.StaffBatchExportView.as_view(), name='staff_batch_export'),
    path('api/departments/batch/delete/', views.DepartmentBatchDeleteView.as_view(), name='department_batch_delete'),
    path('api/positions/batch/delete/', views.PositionBatchDeleteView.as_view(), name='position_batch_delete'),
]

# 匿名端URL配置
anonymous_urlpatterns = [
    path('login/', views.AnonymousLoginView.as_view(), name='anonymous_login'),
    path('logout/', views.AnonymousLogoutView.as_view(), name='anonymous_logout'),
    path('profile/', views.AnonymousProfileView.as_view(), name='anonymous_profile'),
]

urlpatterns = [
    path('admin/', include((admin_urlpatterns, 'admin'))),
    path('anonymous/', include((anonymous_urlpatterns, 'anonymous'))),
]