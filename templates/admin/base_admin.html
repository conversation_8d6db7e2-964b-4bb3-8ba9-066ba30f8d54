{% extends "common/base.html" %}

{% block content %}
<!-- 侧边栏 -->
<div id="sidebar" class="fixed left-0 top-0 h-full bg-white border-r border-gray-200 flex flex-col sidebar-expanded sidebar-transition z-50">
    <!-- Logo Section -->
    <div class="p-4 border-b border-gray-200">
        <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center flex-shrink-0">
                <i data-lucide="clipboard-check" class="w-5 h-5 text-white"></i>
            </div>
            <div class="sidebar-text">
                <h1 class="text-lg font-semibold text-gray-900">考评系统</h1>
                <p class="text-xs text-gray-500">Performance System</p>
            </div>
        </div>
        <!-- Collapse Button -->
        <button id="collapseBtn" class="absolute -right-3 top-6 w-6 h-6 bg-white border border-gray-200 rounded-full flex items-center justify-center hover:bg-gray-50">
            <i data-lucide="chevron-left" class="w-4 h-4 text-gray-600"></i>
        </button>
    </div>

    <!-- Navigation -->
    <nav class="flex-1 p-4 overflow-y-auto">
        <ul class="space-y-1">
            <!-- 总览 -->
            <li>
                <a href="{% url 'organizations:admin:dashboard' %}" 
                   class="nav-link flex items-center space-x-3 px-3 py-2 rounded-md {% if request.resolver_match.url_name == 'dashboard' %}bg-blue-50 text-blue-700 border-r-2 border-blue-600{% else %}text-gray-700 hover:bg-gray-100{% endif %}">
                    <i data-lucide="home" class="w-5 h-5 flex-shrink-0"></i>
                    <span class="sidebar-text {% if request.resolver_match.url_name == 'dashboard' %}font-medium{% endif %}">仪表板</span>
                </a>
            </li>
            
            <!-- 组织管理 -->
            <li class="pt-4">
                <div class="sidebar-text px-3 py-1 text-xs font-semibold text-gray-400 uppercase tracking-wider">组织管理</div>
            </li>
            <li>
                <a href="{% url 'organizations:admin:department_list' %}" 
                   class="nav-link flex items-center space-x-3 px-3 py-2 rounded-md {% if 'department' in request.resolver_match.url_name %}bg-blue-50 text-blue-700 border-r-2 border-blue-600{% else %}text-gray-700 hover:bg-gray-100{% endif %}">
                    <i data-lucide="building" class="w-5 h-5 flex-shrink-0"></i>
                    <span class="sidebar-text {% if 'department' in request.resolver_match.url_name %}font-medium{% endif %}">部门管理</span>
                </a>
            </li>
            <li>
                <a href="{% url 'organizations:admin:position_list' %}" 
                   class="nav-link flex items-center space-x-3 px-3 py-2 rounded-md {% if 'position' in request.resolver_match.url_name %}bg-blue-50 text-blue-700 border-r-2 border-blue-600{% else %}text-gray-700 hover:bg-gray-100{% endif %}">
                    <i data-lucide="briefcase" class="w-5 h-5 flex-shrink-0"></i>
                    <span class="sidebar-text {% if 'position' in request.resolver_match.url_name %}font-medium{% endif %}">职位管理</span>
                </a>
            </li>
            <li>
                <a href="{% url 'organizations:admin:staff_list' %}" 
                   class="nav-link flex items-center space-x-3 px-3 py-2 rounded-md {% if 'staff' in request.resolver_match.url_name %}bg-blue-50 text-blue-700 border-r-2 border-blue-600{% else %}text-gray-700 hover:bg-gray-100{% endif %}">
                    <i data-lucide="users" class="w-5 h-5 flex-shrink-0"></i>
                    <span class="sidebar-text {% if 'staff' in request.resolver_match.url_name %}font-medium{% endif %}">员工管理</span>
                </a>
            </li>
            
            <!-- 安全管理 -->
            <li class="pt-4">
                <div class="sidebar-text px-3 py-1 text-xs font-semibold text-gray-400 uppercase tracking-wider">安全管理</div>
            </li>
            <li>
                <a href="{% url 'organizations:admin:permissions_manage' %}" 
                   class="nav-link flex items-center space-x-3 px-3 py-2 rounded-md {% if 'permissions' in request.resolver_match.url_name %}bg-blue-50 text-blue-700 border-r-2 border-blue-600{% else %}text-gray-700 hover:bg-gray-100{% endif %}">
                    <i data-lucide="shield-check" class="w-5 h-5 flex-shrink-0"></i>
                    <span class="sidebar-text {% if 'permissions' in request.resolver_match.url_name %}font-medium{% endif %}">权限管理</span>
                </a>
            </li>
            <li>
                <a href="{% url 'organizations:admin:anonymous_codes_manage' %}" 
                   class="nav-link flex items-center space-x-3 px-3 py-2 rounded-md {% if 'anonymous' in request.resolver_match.url_name %}bg-blue-50 text-blue-700 border-r-2 border-blue-600{% else %}text-gray-700 hover:bg-gray-100{% endif %}">
                    <i data-lucide="key" class="w-5 h-5 flex-shrink-0"></i>
                    <span class="sidebar-text {% if 'anonymous' in request.resolver_match.url_name %}font-medium{% endif %}">匿名编号</span>
                </a>
            </li>
            
            <!-- 考评管理 -->
            <li class="pt-4">
                <div class="sidebar-text px-3 py-1 text-xs font-semibold text-gray-400 uppercase tracking-wider">考评管理</div>
            </li>
            <li>
                <a href="{% url 'evaluations:admin:template_list' %}" 
                   class="nav-link flex items-center space-x-3 px-3 py-2 rounded-md {% if 'template' in request.resolver_match.url_name %}bg-blue-50 text-blue-700 border-r-2 border-blue-600{% else %}text-gray-700 hover:bg-gray-100{% endif %}">
                    <i data-lucide="file-text" class="w-5 h-5 flex-shrink-0"></i>
                    <span class="sidebar-text {% if 'template' in request.resolver_match.url_name %}font-medium{% endif %}">考评模板</span>
                </a>
            </li>
            <li>
                <a href="{% url 'evaluations:admin:batch_list' %}" 
                   class="nav-link flex items-center space-x-3 px-3 py-2 rounded-md {% if 'batch' in request.resolver_match.url_name %}bg-blue-50 text-blue-700 border-r-2 border-blue-600{% else %}text-gray-700 hover:bg-gray-100{% endif %}">
                    <i data-lucide="calendar" class="w-5 h-5 flex-shrink-0"></i>
                    <span class="sidebar-text {% if 'batch' in request.resolver_match.url_name %}font-medium{% endif %}">考评批次</span>
                </a>
            </li>
            <li>
                <a href="{% url 'evaluations:admin:rule_list' %}" 
                   class="nav-link flex items-center space-x-3 px-3 py-2 rounded-md {% if 'rule' in request.resolver_match.url_name %}bg-blue-50 text-blue-700 border-r-2 border-blue-600{% else %}text-gray-700 hover:bg-gray-100{% endif %}">
                    <i data-lucide="settings" class="w-5 h-5 flex-shrink-0"></i>
                    <span class="sidebar-text {% if 'rule' in request.resolver_match.url_name %}font-medium{% endif %}">权重规则</span>
                </a>
            </li>
            <li>
                <a href="{% url 'evaluations:admin:progress_list' %}" 
                   class="nav-link flex items-center space-x-3 px-3 py-2 rounded-md {% if 'progress' in request.resolver_match.url_name %}bg-blue-50 text-blue-700 border-r-2 border-blue-600{% else %}text-gray-700 hover:bg-gray-100{% endif %}">
                    <i data-lucide="activity" class="w-5 h-5 flex-shrink-0"></i>
                    <span class="sidebar-text {% if 'progress' in request.resolver_match.url_name %}font-medium{% endif %}">进度监控</span>
                </a>
            </li>
            <li>
                <a href="{% url 'evaluations:admin:history:list' %}" 
                   class="nav-link flex items-center space-x-3 px-3 py-2 rounded-md {% if 'history' in request.resolver_match.url_name %}bg-blue-50 text-blue-700 border-r-2 border-blue-600{% else %}text-gray-700 hover:bg-gray-100{% endif %}">
                    <i data-lucide="history" class="w-5 h-5 flex-shrink-0"></i>
                    <span class="sidebar-text {% if 'history' in request.resolver_match.url_name %}font-medium{% endif %}">历史管理</span>
                </a>
            </li>
            
            <!-- 站内通信 -->
            <li class="pt-4">
                <div class="sidebar-text px-3 py-1 text-xs font-semibold text-gray-400 uppercase tracking-wider">站内通信</div>
            </li>
            <li>
                <a href="{% url 'communications:admin:message_center' %}" 
                   class="nav-link flex items-center space-x-3 px-3 py-2 rounded-md {% if 'message' in request.resolver_match.url_name %}bg-blue-50 text-blue-700 border-r-2 border-blue-600{% else %}text-gray-700 hover:bg-gray-100{% endif %}">
                    <i data-lucide="mail" class="w-5 h-5 flex-shrink-0"></i>
                    <span class="sidebar-text {% if 'message' in request.resolver_match.url_name %}font-medium{% endif %}">消息中心</span>
                    <span id="unreadBadge" class="sidebar-text ml-auto hidden">
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 min-w-[18px] h-[18px]">
                            <span id="unreadCount">0</span>
                        </span>
                    </span>
                </a>
            </li>
            <li>
                <a href="{% url 'communications:admin:announcement_list' %}" 
                   class="nav-link flex items-center space-x-3 px-3 py-2 rounded-md {% if 'announcement' in request.resolver_match.url_name %}bg-blue-50 text-blue-700 border-r-2 border-blue-600{% else %}text-gray-700 hover:bg-gray-100{% endif %}">
                    <i data-lucide="megaphone" class="w-5 h-5 flex-shrink-0"></i>
                    <span class="sidebar-text {% if 'announcement' in request.resolver_match.url_name %}font-medium{% endif %}">公告通知</span>
                </a>
            </li>
            
            <!-- 统计分析 -->
            <li class="pt-4">
                <div class="sidebar-text px-3 py-1 text-xs font-semibold text-gray-400 uppercase tracking-wider">统计分析</div>
            </li>
            <li>
                <a href="{% url 'reports:admin:report_list' %}" 
                   class="nav-link flex items-center space-x-3 px-3 py-2 rounded-md {% if 'report' in request.resolver_match.url_name %}bg-blue-50 text-blue-700 border-r-2 border-blue-600{% else %}text-gray-700 hover:bg-gray-100{% endif %}">
                    <i data-lucide="bar-chart-3" class="w-5 h-5 flex-shrink-0"></i>
                    <span class="sidebar-text {% if 'report' in request.resolver_match.url_name %}font-medium{% endif %}">考评报告</span>
                </a>
            </li>
            <li>
                <a href="{% url 'reports:admin:talent_assessment_list' %}" 
                   class="nav-link flex items-center space-x-3 px-3 py-2 rounded-md {% if 'talent' in request.resolver_match.url_name %}bg-blue-50 text-blue-700 border-r-2 border-blue-600{% else %}text-gray-700 hover:bg-gray-100{% endif %}">
                    <i data-lucide="target" class="w-5 h-5 flex-shrink-0"></i>
                    <span class="sidebar-text {% if 'talent' in request.resolver_match.url_name %}font-medium{% endif %}">人才盘点</span>
                </a>
            </li>
            <li>
                <a href="{% url 'reports:admin:analytics' %}" 
                   class="nav-link flex items-center space-x-3 px-3 py-2 rounded-md {% if 'analytics' in request.resolver_match.url_name %}bg-blue-50 text-blue-700 border-r-2 border-blue-600{% else %}text-gray-700 hover:bg-gray-100{% endif %}">
                    <i data-lucide="trending-up" class="w-5 h-5 flex-shrink-0"></i>
                    <span class="sidebar-text {% if 'analytics' in request.resolver_match.url_name %}font-medium{% endif %}">数据分析</span>
                </a>
            </li>
            
            <!-- 快捷访问 -->
            <li class="pt-4">
                <div class="sidebar-text px-3 py-1 text-xs font-semibold text-gray-400 uppercase tracking-wider">快捷访问</div>
            </li>
            <li>
                <a href="{% url 'evaluations:anonymous:home' %}" 
                   class="nav-link flex items-center space-x-3 px-3 py-2 rounded-md text-gray-700 hover:bg-gray-100">
                    <i data-lucide="shield" class="w-5 h-5 flex-shrink-0"></i>
                    <span class="sidebar-text">匿名考评端</span>
                </a>
            </li>
        </ul>
    </nav>

    <!-- User Info -->
    <div class="p-4 border-t border-gray-200">
        <div class="flex items-center space-x-3">
            <a href="{% url 'organizations:admin:profile_management' %}" class="flex items-center space-x-3 flex-1 rounded-lg hover:bg-gray-50 transition-colors p-2 -m-2" title="个人资料管理">
                <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center flex-shrink-0">
                    <span class="text-sm font-medium">{{ request.current_staff.name|first|default:"管" }}</span>
                </div>
                <div class="sidebar-text flex-1">
                    <p class="text-sm font-medium text-gray-900">{{ request.current_staff.name|default:"管理员" }}</p>
                    <p class="text-xs text-gray-500">{{ request.current_staff.get_role_display|default:"管理员" }}</p>
                </div>
            </a>
            <div class="sidebar-text">
                <button id="logout-btn" class="p-1 text-gray-400 hover:text-red-600 transition-colors" title="退出登录">
                    <i data-lucide="log-out" class="w-4 h-4"></i>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Main Content -->
<div id="mainContent" class="content-expanded content-transition">
    <!-- Header -->
    <div class="main-header">
        <div class="flex items-center justify-between w-full min-h-full">
            <div class="flex-shrink-0">
                <h1 class="text-2xl font-bold text-gray-900 leading-tight">{% block page_title %}管理后台{% endblock %}</h1>
                <p class="text-gray-600 text-sm mt-1 leading-tight">{% block page_description %}欢迎回来，查看最新的考评数据{% endblock %}</p>
            </div>
            <div class="flex items-center space-x-3 flex-shrink-0 ml-4">
                <!-- 消息通知按钮 -->
                <div class="relative">
                    <button id="messageNotificationBtn" onclick="toggleMessageDropdown()" class="p-2 text-gray-400 hover:text-gray-600 relative flex-shrink-0" title="消息通知">
                        <i data-lucide="mail" class="w-5 h-5"></i>
                        <span id="messageNotificationBadge" class="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full text-xs text-white flex items-center justify-center hidden">
                            <span id="topUnreadCount">0</span>
                        </span>
                    </button>
                    
                    <!-- 消息下拉菜单 -->
                    <div id="messageDropdown" class="absolute right-0 mt-2 w-80 bg-white rounded-md shadow-lg z-50 border border-gray-200 hidden">
                        <div class="px-4 py-3 border-b border-gray-200">
                            <div class="flex items-center justify-between">
                                <h3 class="text-sm font-medium text-gray-900">消息通知</h3>
                                <a href="{% url 'communications:admin:message_center' %}" class="text-xs text-blue-600 hover:text-blue-800">查看全部</a>
                            </div>
                        </div>
                        
                        <div id="messageDropdownContent" class="max-h-96 overflow-y-auto">
                            <div class="px-4 py-8 text-center text-gray-500">
                                <i data-lucide="mail" class="w-8 h-8 mx-auto mb-2 text-gray-400"></i>
                                <p class="text-sm">加载中...</p>
                            </div>
                        </div>
                        
                        <div class="px-4 py-3 border-t border-gray-200">
                            <a href="{% url 'communications:admin:message_center' %}" class="text-sm text-blue-600 hover:text-blue-800 flex items-center justify-center">
                                <i data-lucide="mail" class="w-4 h-4 mr-1"></i>
                                进入消息中心
                            </a>
                        </div>
                    </div>
                </div>
                
                <button class="p-2 text-gray-400 hover:text-gray-600 flex-shrink-0" title="搜索">
                    <i data-lucide="search" class="w-5 h-5"></i>
                </button>
                {% block header_actions %}
                <button onclick="showNotification('功能开发中', 'info')" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center space-x-2 flex-shrink-0 whitespace-nowrap">
                    <i data-lucide="plus" class="w-4 h-4"></i>
                    <span>新建考评</span>
                </button>
                {% endblock %}
            </div>
        </div>
    </div>

    <!-- Content -->
    <div class="main-content">
        {% block admin_content %}{% endblock %}
    </div>
</div>

<script>
    // 侧边栏折叠功能
    document.addEventListener('DOMContentLoaded', function() {
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.getElementById('mainContent');
        const collapseBtn = document.getElementById('collapseBtn');
        const sidebarTexts = document.querySelectorAll('.sidebar-text');
        
        let isCollapsed = false;
        let isAnimating = false;

        if (collapseBtn) {
            collapseBtn.addEventListener('click', function(e) {
                e.preventDefault();
                if (isAnimating) return; // 防止动画期间重复点击
                
                isAnimating = true;
                isCollapsed = !isCollapsed;
                
                // 添加性能优化
                sidebar.style.willChange = 'width';
                mainContent.style.willChange = 'margin-left';
                
                if (isCollapsed) {
                    sidebar.classList.remove('sidebar-expanded');
                    sidebar.classList.add('sidebar-collapsed');
                    mainContent.classList.remove('content-expanded');
                    mainContent.classList.add('content-collapsed');
                    
                    // 隐藏文本元素
                    sidebarTexts.forEach(text => {
                        text.style.display = 'none';
                    });
                    
                    // 旋转折叠按钮
                    const icon = collapseBtn.querySelector('i');
                    if (icon) icon.style.transform = 'rotate(180deg)';
                } else {
                    sidebar.classList.remove('sidebar-collapsed');
                    sidebar.classList.add('sidebar-expanded');
                    mainContent.classList.remove('content-collapsed');
                    mainContent.classList.add('content-expanded');
                    
                    // 延迟显示文本元素，等待动画完成
                    setTimeout(() => {
                        sidebarTexts.forEach(text => {
                            text.style.display = 'block';
                        });
                    }, 150);
                    
                    // 重置折叠按钮
                    const icon = collapseBtn.querySelector('i');
                    if (icon) icon.style.transform = 'rotate(0deg)';
                }
                
                // 清理性能优化并重置动画标志
                setTimeout(() => {
                    sidebar.style.willChange = 'auto';
                    mainContent.style.willChange = 'auto';
                    isAnimating = false;
                }, 200);
            });
        }

        // 优化导航链接点击响应
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                // 添加点击反馈
                this.style.transform = 'translateX(1px)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 50);
            });
        });

        // 消息通知功能初始化
        initializeMessageNotifications();
        
        // 定期检查未读消息
        setInterval(checkUnreadMessages, 30000); // 每30秒检查一次
        
        // 初始加载未读消息数量
        checkUnreadMessages();
    });

    // 消息通知相关函数
    function initializeMessageNotifications() {
        // 点击外部关闭下拉菜单
        document.addEventListener('click', function(e) {
            const dropdown = document.getElementById('messageDropdown');
            const button = document.getElementById('messageNotificationBtn');
            
            if (!dropdown.contains(e.target) && !button.contains(e.target)) {
                dropdown.classList.add('hidden');
            }
        });
    }

    // 切换消息下拉菜单
    function toggleMessageDropdown() {
        const dropdown = document.getElementById('messageDropdown');
        const isHidden = dropdown.classList.contains('hidden');
        
        if (isHidden) {
            dropdown.classList.remove('hidden');
            loadRecentMessages();
        } else {
            dropdown.classList.add('hidden');
        }
    }

    // 检查未读消息数量（使用JWT认证管理器）
    async function checkUnreadMessages() {
        if (!authManager.isAuthenticated()) return;

        try {
            const response = await authManager.authenticatedRequest('/communications/api/messages/unread-count/');
            if (response && response.ok) {
                const data = await response.json();
                if (data.success) {
                    updateUnreadBadges(data.count);
                }
            }
        } catch (error) {
            console.error('检查未读消息失败:', error);
        }
    }

    // 更新未读消息徽章
    function updateUnreadBadges(count) {
        const topBadge = document.getElementById('messageNotificationBadge');
        const topCount = document.getElementById('topUnreadCount');
        const sidebarBadge = document.getElementById('unreadBadge');
        const sidebarCount = document.getElementById('unreadCount');
        
        if (count > 0) {
            // 显示顶部通知徽章
            if (topBadge && topCount) {
                topBadge.classList.remove('hidden');
                topCount.textContent = count > 99 ? '99+' : count;
            }
            
            // 显示侧边栏徽章
            if (sidebarBadge && sidebarCount) {
                sidebarBadge.classList.remove('hidden');
                sidebarCount.textContent = count > 99 ? '99+' : count;
            }
        } else {
            // 隐藏徽章
            if (topBadge) topBadge.classList.add('hidden');
            if (sidebarBadge) sidebarBadge.classList.add('hidden');
        }
    }

    // 加载最近消息（使用JWT认证管理器）
    async function loadRecentMessages() {
        const content = document.getElementById('messageDropdownContent');
        
        if (!authManager.isAuthenticated()) {
            content.innerHTML = `
                <div class="px-4 py-8 text-center text-gray-500">
                    <i data-lucide="alert-circle" class="w-8 h-8 mx-auto mb-2 text-red-400"></i>
                    <p class="text-sm">请先登录</p>
                </div>
            `;
            return;
        }
        
        // 显示加载状态
        content.innerHTML = `
            <div class="px-4 py-8 text-center text-gray-500">
                <i data-lucide="loader-2" class="w-6 h-6 mx-auto mb-2 text-gray-400 animate-spin"></i>
                <p class="text-sm">加载中...</p>
            </div>
        `;
        
        try {
            const response = await authManager.authenticatedRequest('/communications/api/messages/recent/');
            if (response && response.ok) {
                const data = await response.json();
                if (data.success && data.messages.length > 0) {
                    content.innerHTML = data.messages.map(message => `
                        <div class="px-4 py-3 hover:bg-gray-50 border-b border-gray-100 last:border-b-0">
                            <div class="flex items-start space-x-3">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 rounded-full flex items-center justify-center
                                        ${message.message_type === 'SYSTEM' ? 'bg-blue-100' : ''}
                                        ${message.message_type === 'PERSONAL' ? 'bg-green-100' : ''}
                                        ${message.message_type === 'EVALUATION' ? 'bg-purple-100' : ''}
                                        ${message.message_type === 'ANNOUNCEMENT' ? 'bg-yellow-100' : ''}">
                                        <i data-lucide="
                                            ${message.message_type === 'SYSTEM' ? 'settings' : ''}
                                            ${message.message_type === 'PERSONAL' ? 'user' : ''}
                                            ${message.message_type === 'EVALUATION' ? 'clipboard-check' : ''}
                                            ${message.message_type === 'ANNOUNCEMENT' ? 'megaphone' : ''}
                                        " class="w-4 h-4 
                                            ${message.message_type === 'SYSTEM' ? 'text-blue-600' : ''}
                                            ${message.message_type === 'PERSONAL' ? 'text-green-600' : ''}
                                            ${message.message_type === 'EVALUATION' ? 'text-purple-600' : ''}
                                            ${message.message_type === 'ANNOUNCEMENT' ? 'text-yellow-600' : ''}
                                        "></i>
                                    </div>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <a href="/communications/admin/messages/${message.id}/" class="block">
                                        <p class="text-sm font-medium text-gray-900 truncate ${!message.is_read ? 'font-semibold' : ''}">
                                            ${message.subject}
                                        </p>
                                        <p class="text-xs text-gray-500 mt-1 truncate">
                                            ${message.sender_name || '系统'} • ${message.created_at}
                                        </p>
                                        ${!message.is_read ? '<div class="w-2 h-2 bg-blue-500 rounded-full mt-1"></div>' : ''}
                                    </a>
                                </div>
                            </div>
                        </div>
                    `).join('');
                } else {
                    content.innerHTML = `
                        <div class="px-4 py-8 text-center text-gray-500">
                            <i data-lucide="mail" class="w-8 h-8 mx-auto mb-2 text-gray-400"></i>
                            <p class="text-sm">暂无新消息</p>
                        </div>
                    `;
                }
                
                // 重新初始化图标
                if (typeof lucide !== 'undefined') {
                    lucide.createIcons();
                }
            }
        } catch (error) {
            console.error('加载消息失败:', error);
            content.innerHTML = `
                <div class="px-4 py-8 text-center text-gray-500">
                    <i data-lucide="alert-circle" class="w-8 h-8 mx-auto mb-2 text-red-400"></i>
                    <p class="text-sm">加载失败，请稍后重试</p>
                </div>
            `;
            
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }
        }
    }

    // JWT退出功能（使用JWT认证管理器）
    async function handleLogout() {
        if (confirm('确定要退出登录吗？')) {
            try {
                await authManager.logout();
            } catch (error) {
                console.error('退出登录失败:', error);
                // 即使失败也清理并跳转
                authManager.clearTokens();
                authManager.redirectToLogin();
            }
        }
    }

    // 绑定退出按钮事件
    const logoutBtn = document.getElementById('logout-btn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', handleLogout);
    }


    // JWT认证管理器会自动处理认证检查
    // 初始化消息检查
    if (authManager.isAuthenticated()) {
        // 初始加载未读消息数量
        checkUnreadMessages();
        
        // 定期检查未读消息（使用JWT认证）
        setInterval(checkUnreadMessages, 30000); // 每30秒检查一次
    } else if (!window.location.pathname.includes('login')) {
        // 如果未登录且不是登录页面，跳转到登录页
        authManager.redirectToLogin();
    }
});
</script>
{% endblock %}