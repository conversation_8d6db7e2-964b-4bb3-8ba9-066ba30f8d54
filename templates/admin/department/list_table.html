{% extends "admin/base_admin.html" %}
{% load static %}

{% block page_title %}部门管理{% endblock %}
{% block page_description %}管理公司组织架构和部门信息{% endblock %}

{% block header_actions %}
<div class="flex items-center space-x-3">
    <!-- 视图切换按钮 -->
    <div class="flex bg-gray-100 rounded-lg p-1">
        <button id="table-view-btn" class="px-3 py-1 text-sm font-medium rounded-md bg-white text-gray-900 shadow-sm">
            <i data-lucide="list" class="w-4 h-4 mr-1 inline"></i>
            表格视图
        </button>
        <button id="card-view-btn" class="px-3 py-1 text-sm font-medium rounded-md text-gray-600 hover:text-gray-900">
            <i data-lucide="grid-3x3" class="w-4 h-4 mr-1 inline"></i>
            卡片视图
        </button>
    </div>
    
    <a href="{% url 'organizations:admin:department_import' %}" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center space-x-2">
        <i data-lucide="upload" class="w-4 h-4"></i>
        <span>导入部门</span>
    </a>
    <a href="{% url 'organizations:admin:department_export' %}" class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 flex items-center space-x-2">
        <i data-lucide="download" class="w-4 h-4"></i>
        <span>导出部门</span>
    </a>
    <a href="{% url 'organizations:admin:department_create' %}" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center space-x-2">
        <i data-lucide="plus" class="w-4 h-4"></i>
        <span>新建部门</span>
    </a>
</div>
{% endblock %}

{% block admin_content %}
<!-- Stats -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-blue-100 rounded-lg">
                <i data-lucide="building" class="w-6 h-6 text-blue-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">总部门数</p>
                <p class="text-2xl font-bold text-gray-900">{{ stats.total_departments|default:0 }}</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-green-100 rounded-lg">
                <i data-lucide="check-circle" class="w-6 h-6 text-green-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">启用部门</p>
                <p class="text-2xl font-bold text-gray-900">{{ stats.active_departments|default:0 }}</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-purple-100 rounded-lg">
                <i data-lucide="users" class="w-6 h-6 text-purple-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">总员工数</p>
                <p class="text-2xl font-bold text-gray-900">{{ stats.total_staff|default:0 }}</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-orange-100 rounded-lg">
                <i data-lucide="building-2" class="w-6 h-6 text-orange-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">有员工部门</p>
                <p class="text-2xl font-bold text-gray-900">{{ stats.departments_with_staff|default:0 }}</p>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filter -->
<div class="bg-white rounded-lg shadow mb-6">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
            <div class="flex-1 max-w-lg">
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i data-lucide="search" class="w-5 h-5 text-gray-400"></i>
                    </div>
                    <input type="text" id="searchInput" placeholder="搜索部门名称或编码..." 
                           class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                </div>
            </div>
            
            <div class="flex items-center space-x-4">
                <!-- 快速筛选标签 -->
                <div class="flex space-x-2">
                    <button class="filter-tag px-3 py-1 text-sm rounded-full border bg-blue-500 text-white" data-filter="all">
                        全部部门
                    </button>
                    <button class="filter-tag px-3 py-1 text-sm rounded-full border border-gray-300 text-gray-700 hover:bg-gray-50" data-filter="active">
                        启用部门
                    </button>
                    <button class="filter-tag px-3 py-1 text-sm rounded-full border border-gray-300 text-gray-700 hover:bg-gray-50" data-filter="with-staff">
                        有员工部门
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Table -->
<div class="bg-white shadow rounded-lg overflow-hidden">
    <!-- Table Header with Batch Actions -->
    <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <label class="flex items-center">
                    <input type="checkbox" id="selectAll" class="checkbox">
                    <span class="ml-2 text-sm text-gray-700">全选</span>
                </label>
                <span id="selectedCount" class="text-sm text-gray-500">已选择 0 项</span>
            </div>
            
            <!-- Batch Actions -->
            <div id="batchActions" class="hidden flex items-center space-x-2">
                <button class="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700">
                    批量编辑
                </button>
                <button onclick="showBatchDeleteConfirm()" class="px-3 py-1 text-sm bg-red-600 text-white rounded hover:bg-red-700">
                    批量删除
                </button>
            </div>
        </div>
    </div>

    <!-- Table Content -->
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        选择
                    </th>
                    <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" data-sort="name">
                        <div class="flex items-center space-x-1">
                            <span>部门信息</span>
                            <i data-lucide="arrow-up-down" class="w-3 h-3"></i>
                        </div>
                    </th>
                    <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" data-sort="parent_department">
                        <div class="flex items-center space-x-1">
                            <span>上级部门</span>
                            <i data-lucide="arrow-up-down" class="w-3 h-3"></i>
                        </div>
                    </th>
                    <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        负责人
                    </th>
                    <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        员工数量
                    </th>
                    <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        状态
                    </th>
                    <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" data-sort="created_at">
                        <div class="flex items-center space-x-1">
                            <span>创建时间</span>
                            <i data-lucide="arrow-up-down" class="w-3 h-3"></i>
                        </div>
                    </th>
                    <th scope="col" class="relative px-3 py-3">
                        <span class="sr-only">操作</span>
                    </th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200" id="departmentsTableBody">
                {% if object_list %}
                    {% for department in object_list %}
                    <tr class="hover:bg-gray-50 department-row" 
                        data-active="{{ department.is_active|yesno:'true,false' }}"
                        data-has-staff="{% if department.get_staff_count > 0 %}true{% else %}false{% endif %}">
                        <td class="px-3 py-4 whitespace-nowrap">
                            <input type="checkbox" class="row-checkbox checkbox" value="{{ department.pk }}">
                        </td>
                        <td class="px-3 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="p-2 bg-blue-100 rounded-lg mr-3">
                                    <i data-lucide="building" class="w-4 h-4 text-blue-600"></i>
                                </div>
                                <div>
                                    <div class="text-sm font-medium text-gray-900">{{ department.name }}</div>
                                    <div class="text-sm text-gray-500">{{ department.dept_code }}</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-3 py-4 whitespace-nowrap">
                            {% if department.parent_department %}
                                <div class="text-sm text-gray-900">{{ department.parent_department.name }}</div>
                                <div class="text-sm text-gray-500">{{ department.parent_department.dept_code }}</div>
                            {% else %}
                                <span class="text-sm text-gray-400">顶级部门</span>
                            {% endif %}
                        </td>
                        <td class="px-3 py-4 whitespace-nowrap">
                            {% if department.manager %}
                                <div class="text-sm text-gray-900">{{ department.manager.name }}</div>
                                <div class="text-sm text-gray-500">{{ department.manager.staff_id }}</div>
                            {% else %}
                                <span class="text-sm text-gray-400">未设置</span>
                            {% endif %}
                        </td>
                        <td class="px-3 py-4 whitespace-nowrap">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                {{ department.get_staff_count|default:0 }} 人
                            </span>
                        </td>
                        <td class="px-3 py-4 whitespace-nowrap">
                            {% if department.is_active %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <i data-lucide="check-circle" class="w-3 h-3 mr-1"></i>
                                    启用
                                </span>
                            {% else %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    <i data-lucide="x-circle" class="w-3 h-3 mr-1"></i>
                                    禁用
                                </span>
                            {% endif %}
                        </td>
                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ department.created_at|date:"Y-m-d" }}
                        </td>
                        <td class="px-3 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div class="flex items-center space-x-2">
                                <a href="{% url 'organizations:admin:department_detail' department.pk %}" 
                                   class="text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-blue-50" title="查看详情">
                                    <i data-lucide="eye" class="w-4 h-4"></i>
                                </a>
                                <a href="{% url 'organizations:admin:department_update' department.pk %}" 
                                   class="text-green-600 hover:text-green-900 p-1 rounded hover:bg-green-50" title="编辑">
                                    <i data-lucide="edit" class="w-4 h-4"></i>
                                </a>
                                <button onclick="confirmDelete('{{ department.pk }}', '{{ department.name }}')" 
                                        class="text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-50" title="删除">
                                    <i data-lucide="trash-2" class="w-4 h-4"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                {% else %}
                    <tr>
                        <td colspan="8" class="px-3 py-12 text-center">
                            <div class="flex flex-col items-center">
                                <i data-lucide="building" class="w-12 h-12 text-gray-400 mb-4"></i>
                                <h3 class="text-sm font-medium text-gray-900 mb-2">暂无部门数据</h3>
                                <p class="text-sm text-gray-500 mb-4">开始创建第一个部门吧</p>
                                <a href="{% url 'organizations:admin:department_create' %}" 
                                   class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                                    <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                                    新建部门
                                </a>
                            </div>
                        </td>
                    </tr>
                {% endif %}
            </tbody>
        </table>
    </div>
</div>

<!-- Pagination -->
{% if is_paginated %}
<div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6 mt-6">
    <div class="flex-1 flex justify-between sm:hidden">
        {% if page_obj.has_previous %}
            <a href="?page={{ page_obj.previous_page_number }}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                上一页
            </a>
        {% endif %}
        {% if page_obj.has_next %}
            <a href="?page={{ page_obj.next_page_number }}" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                下一页
            </a>
        {% endif %}
    </div>
    <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
        <div>
            <p class="text-sm text-gray-700">
                显示第 <span class="font-medium">{{ page_obj.start_index }}</span> 到 <span class="font-medium">{{ page_obj.end_index }}</span> 条，
                共 <span class="font-medium">{{ page_obj.paginator.count }}</span> 条记录
            </p>
        </div>
        <div>
            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                {% if page_obj.has_previous %}
                    <a href="?page={{ page_obj.previous_page_number }}" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        <i data-lucide="chevron-left" class="w-4 h-4"></i>
                    </a>
                {% endif %}

                {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                        <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600">
                            {{ num }}
                        </span>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <a href="?page={{ num }}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                            {{ num }}
                        </a>
                    {% endif %}
                {% endfor %}

                {% if page_obj.has_next %}
                    <a href="?page={{ page_obj.next_page_number }}" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        <i data-lucide="chevron-right" class="w-4 h-4"></i>
                    </a>
                {% endif %}
            </nav>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    initializeTableFeatures();
    initializeSearch();
    initializeFilters();
    initializeViewSwitcher();

    // 初始化图标
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
});

// 初始化表格功能
function initializeTableFeatures() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const rowCheckboxes = document.querySelectorAll('.row-checkbox');

    // 全选功能
    selectAllCheckbox?.addEventListener('change', function() {
        rowCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateSelectedCount();
        toggleBatchActions();
    });

    // 行选择功能
    rowCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateSelectedCount();
            toggleBatchActions();

            // 更新全选状态
            const checkedCount = document.querySelectorAll('.row-checkbox:checked').length;
            selectAllCheckbox.checked = checkedCount === rowCheckboxes.length;
            selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < rowCheckboxes.length;
        });
    });

    // 排序功能
    const sortHeaders = document.querySelectorAll('[data-sort]');
    sortHeaders.forEach(header => {
        header.addEventListener('click', function() {
            const sortBy = this.getAttribute('data-sort');
            sortTable(sortBy);
        });
    });
}

// 初始化搜索功能
function initializeSearch() {
    const searchInput = document.getElementById('searchInput');

    searchInput?.addEventListener('input', debounce(function() {
        const searchTerm = this.value.toLowerCase();
        filterTable();
    }, 300));

    // 自动聚焦搜索框
    searchInput?.focus();
}

// 初始化筛选功能
function initializeFilters() {
    const filterTags = document.querySelectorAll('.filter-tag');

    // 快速筛选标签
    filterTags.forEach(tag => {
        tag.addEventListener('click', function() {
            // 移除其他标签的激活状态
            filterTags.forEach(t => t.classList.remove('bg-blue-500', 'text-white'));
            // 激活当前标签
            this.classList.add('bg-blue-500', 'text-white');

            const filterType = this.getAttribute('data-filter');
            applyQuickFilter(filterType);
        });
    });
}

// 视图切换功能
function initializeViewSwitcher() {
    const tableViewBtn = document.getElementById('table-view-btn');
    const cardViewBtn = document.getElementById('card-view-btn');

    cardViewBtn?.addEventListener('click', function() {
        // 跳转到卡片视图，保持当前页码和其他参数
        const url = new URL(window.location);
        url.searchParams.set('view', 'card'); // 设置view参数为card
        window.location.href = url.toString();
    });
}

// 表格筛选
function filterTable() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const rows = document.querySelectorAll('.department-row');
    let visibleCount = 0;

    rows.forEach(row => {
        const departmentName = row.querySelector('td:nth-child(2) .text-gray-900').textContent.toLowerCase();
        const departmentCode = row.querySelector('td:nth-child(2) .text-gray-500').textContent.toLowerCase();
        const parentDept = row.querySelector('td:nth-child(3)').textContent.toLowerCase();
        const manager = row.querySelector('td:nth-child(4)').textContent.toLowerCase();

        const matchesSearch = !searchTerm ||
            departmentName.includes(searchTerm) ||
            departmentCode.includes(searchTerm) ||
            parentDept.includes(searchTerm) ||
            manager.includes(searchTerm);

        if (matchesSearch) {
            row.style.display = '';
            visibleCount++;
        } else {
            row.style.display = 'none';
        }
    });

    updateFilterCount(visibleCount);
}

// 快速筛选
function applyQuickFilter(filterType) {
    const rows = document.querySelectorAll('.department-row');
    let visibleCount = 0;

    rows.forEach(row => {
        let showRow = true;

        switch (filterType) {
            case 'all':
                showRow = true;
                break;
            case 'active':
                showRow = row.getAttribute('data-active') === 'true';
                break;
            case 'with-staff':
                showRow = row.getAttribute('data-has-staff') === 'true';
                break;
        }

        if (showRow) {
            row.style.display = '';
            visibleCount++;
        } else {
            row.style.display = 'none';
        }
    });

    updateFilterCount(visibleCount);
}

// 表格排序
function sortTable(sortBy) {
    const tbody = document.getElementById('departmentsTableBody');
    const rows = Array.from(tbody.querySelectorAll('.department-row'));

    rows.sort((a, b) => {
        let aValue, bValue;

        switch (sortBy) {
            case 'name':
                aValue = a.querySelector('td:nth-child(2) .text-gray-900').textContent.trim();
                bValue = b.querySelector('td:nth-child(2) .text-gray-900').textContent.trim();
                return aValue.localeCompare(bValue);
            case 'parent_department':
                aValue = a.querySelector('td:nth-child(3)').textContent.trim();
                bValue = b.querySelector('td:nth-child(3)').textContent.trim();
                return aValue.localeCompare(bValue);
            case 'created_at':
                aValue = a.querySelector('td:nth-child(7)').textContent.trim();
                bValue = b.querySelector('td:nth-child(7)').textContent.trim();
                return new Date(aValue) - new Date(bValue);
            default:
                return 0;
        }
    });

    // 重新排列行
    rows.forEach(row => tbody.appendChild(row));
}

// 更新选中数量
function updateSelectedCount() {
    const checkedCount = document.querySelectorAll('.row-checkbox:checked').length;
    const selectedCountElement = document.getElementById('selectedCount');
    if (selectedCountElement) {
        selectedCountElement.textContent = `已选择 ${checkedCount} 项`;
    }
}

// 切换批量操作按钮显示
function toggleBatchActions() {
    const checkedCount = document.querySelectorAll('.row-checkbox:checked').length;
    const batchActions = document.getElementById('batchActions');

    if (batchActions) {
        if (checkedCount > 0) {
            batchActions.classList.remove('hidden');
        } else {
            batchActions.classList.add('hidden');
        }
    }
}

// 更新筛选计数
function updateFilterCount(count) {
    console.log(`显示 ${count} 条记录`);
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 删除确认
function confirmDelete(departmentId, departmentName) {
    if (confirm(`确定要删除部门 "${departmentName}" 吗？此操作不可撤销。`)) {
        window.location.href = `/admin/departments/${departmentId}/delete/`;
    }
}

// 批量删除确认
function showBatchDeleteConfirm() {
    const selectedCheckboxes = document.querySelectorAll('.row-checkbox:checked');
    
    if (selectedCheckboxes.length === 0) {
        alert('请先选择要删除的部门');
        return;
    }
    
    const departmentNames = [];
    const departmentIds = [];
    
    selectedCheckboxes.forEach(checkbox => {
        const row = checkbox.closest('tr');
        if (row) {
            const departmentName = row.querySelector('td:nth-child(2) .text-gray-900').textContent.trim();
            departmentNames.push(departmentName);
            departmentIds.push(checkbox.value);
        }
    });
    
    const confirmMessage = `确定要删除以下 ${departmentNames.length} 个部门吗？\n\n${departmentNames.join('\n')}\n\n此操作不可撤销！`;
    
    if (confirm(confirmMessage)) {
        performBatchDelete(departmentIds);
    }
}

// 执行批量删除
function performBatchDelete(departmentIds) {
    const csrfToken = getCookie('csrftoken');
    
    // 显示加载状态
    const batchActions = document.getElementById('batchActions');
    const originalHTML = batchActions.innerHTML;
    batchActions.innerHTML = '<div class="text-sm text-gray-600">删除中...</div>';
    
    fetch('{% url "organizations:admin:department_batch_delete" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken
        },
        body: JSON.stringify({
            department_ids: departmentIds
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(`成功删除 ${data.deleted_count} 个部门`);
            location.reload(); // 刷新页面
        } else {
            if (data.dependencies && data.dependencies.length > 0) {
                alert(`${data.message}\n\n${data.dependencies.join('\n')}`);
            } else {
                alert(data.message || '删除失败');
            }
        }
    })
    .catch(error => {
        console.error('批量删除失败:', error);
        alert('删除失败，请重试');
    })
    .finally(() => {
        // 恢复按钮状态
        batchActions.innerHTML = originalHTML;
    });
}

// 获取CSRF Token
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}
</script>
{% endblock %}
